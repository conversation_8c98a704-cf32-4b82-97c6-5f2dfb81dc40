#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
七牛云文件上传工具
用于将Android Studio等大型软件分发到七牛云CDN
"""

import os
import json
import hashlib
from datetime import datetime
from qiniu import Auth, put_file, BucketManager
import requests

class QiniuUploader:
    def __init__(self, access_key, secret_key, bucket_name, domain):
        """
        初始化七牛云上传器
        
        Args:
            access_key: 七牛云AccessKey
            secret_key: 七牛云SecretKey  
            bucket_name: 存储空间名称
            domain: CDN加速域名
        """
        self.auth = Auth(access_key, secret_key)
        self.bucket_name = bucket_name
        self.domain = domain
        self.bucket_manager = BucketManager(self.auth)
    
    def generate_upload_token(self, key=None, expires=3600):
        """
        生成上传凭证
        
        Args:
            key: 文件key，None表示不指定
            expires: 过期时间（秒）
        """
        policy = {
            'scope': f'{self.bucket_name}:{key}' if key else self.bucket_name,
            'deadline': int(datetime.now().timestamp()) + expires,
            'insertOnly': 1,  # 只允许新增文件
            'fsizeLimit': 10 * 1024 * 1024 * 1024,  # 10GB限制
            'mimeLimit': 'application/octet-stream;application/zip;application/x-msdownload;application/x-apple-diskimage'
        }
        return self.auth.upload_token(self.bucket_name, key, expires, policy)
    
    def upload_file(self, local_file_path, remote_key, progress_callback=None):
        """
        上传文件到七牛云
        
        Args:
            local_file_path: 本地文件路径
            remote_key: 远程文件key
            progress_callback: 进度回调函数
        """
        if not os.path.exists(local_file_path):
            raise FileNotFoundError(f"本地文件不存在: {local_file_path}")
        
        # 生成上传凭证
        token = self.generate_upload_token(remote_key)
        
        # 上传文件
        ret, info = put_file(
            token, 
            remote_key, 
            local_file_path,
            progress_handler=progress_callback
        )
        
        if info.status_code == 200:
            file_url = f"https://{self.domain}/{remote_key}"
            print(f"✅ 上传成功: {file_url}")
            return {
                'success': True,
                'url': file_url,
                'key': remote_key,
                'hash': ret.get('hash'),
                'size': os.path.getsize(local_file_path)
            }
        else:
            print(f"❌ 上传失败: {info}")
            return {'success': False, 'error': info}
    
    def upload_android_studio(self, installer_path, version, platform='windows'):
        """
        上传Android Studio安装包
        
        Args:
            installer_path: 安装包本地路径
            version: 版本号，如 '2025.1.1'
            platform: 平台，如 'windows', 'mac', 'linux'
        """
        file_name = os.path.basename(installer_path)
        remote_key = f"android-studio/{platform}/{version}/{file_name}"
        
        def progress_callback(progress, total):
            percent = (progress / total) * 100
            print(f"\r📤 上传进度: {percent:.1f}% ({progress}/{total} bytes)", end='')
        
        result = self.upload_file(installer_path, remote_key, progress_callback)
        print()  # 换行
        
        if result['success']:
            # 更新版本信息文件
            self.update_version_info(version, platform, result)
        
        return result
    
    def update_version_info(self, version, platform, file_info):
        """
        更新版本信息JSON文件
        """
        version_key = f"android-studio/{platform}/version.json"
        
        # 尝试获取现有版本信息
        try:
            ret, info = self.bucket_manager.stat(self.bucket_name, version_key)
            if info.status_code == 200:
                # 下载现有版本信息
                version_url = f"https://{self.domain}/{version_key}"
                response = requests.get(version_url)
                version_data = response.json()
            else:
                version_data = {'versions': {}}
        except:
            version_data = {'versions': {}}
        
        # 更新版本信息
        version_data['versions'][version] = {
            'url': file_info['url'],
            'size': file_info['size'],
            'hash': file_info['hash'],
            'upload_time': datetime.now().isoformat(),
            'platform': platform
        }
        version_data['latest'] = version
        
        # 保存到临时文件并上传
        temp_file = f"temp_version_{platform}.json"
        with open(temp_file, 'w', encoding='utf-8') as f:
            json.dump(version_data, f, indent=2, ensure_ascii=False)
        
        self.upload_file(temp_file, version_key)
        os.remove(temp_file)
    
    def list_files(self, prefix=''):
        """
        列出存储空间中的文件
        """
        ret, eof, info = self.bucket_manager.list(self.bucket_name, prefix=prefix)
        if info.status_code == 200:
            return ret.get('items', [])
        return []
    
    def delete_file(self, key):
        """
        删除文件
        """
        ret, info = self.bucket_manager.delete(self.bucket_name, key)
        return info.status_code == 200
    
    def refresh_cache(self, urls):
        """
        刷新CDN缓存
        """
        from qiniu import CdnManager
        cdn_manager = CdnManager(self.auth)
        ret, info = cdn_manager.refresh_urls(urls)
        return info.status_code == 200

def main():
    """
    示例用法
    """
    # 配置信息（请替换为您的实际配置）
    config = {
        'access_key': 'YOUR_ACCESS_KEY',
        'secret_key': 'YOUR_SECRET_KEY', 
        'bucket_name': 'android-studio-dist',
        'domain': 'cdn.yourdomain.com'
    }
    
    uploader = QiniuUploader(**config)
    
    # 示例：上传Android Studio安装包
    # installer_path = r"E:\Downloads\android-studio-2025.1.1-windows.exe"
    # result = uploader.upload_android_studio(installer_path, "2025.1.1", "windows")
    
    print("七牛云上传器已初始化，请调用相应方法进行文件上传")

if __name__ == "__main__":
    main()
