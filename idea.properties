# Use ${idea.home.path} macro to specify location relative to IDE installation home.
# Use ${xxx} where xxx is any Java property (including defined in previous lines of this file) to refer to its value.
# Note for Windows users: please make sure you're using forward slashes: C:/dir1/dir2.

#---------------------------------------------------------------------
# Uncomment this option if you want to customize a path to the settings directory.
#---------------------------------------------------------------------
# idea.config.path=${user.home}/.AndroidStudio2025.1.1/config

#---------------------------------------------------------------------
# Uncomment this option if you want to customize a path to the caches directory.
#---------------------------------------------------------------------
# idea.system.path=${user.home}/.AndroidStudio2025.1.1/system

#---------------------------------------------------------------------
# Uncomment this option if you want to customize a path to the user-installed plugins directory.
#---------------------------------------------------------------------
# idea.plugins.path=${idea.config.path}/plugins

#---------------------------------------------------------------------
# Uncomment this option if you want to customize a path to the logs directory.
#---------------------------------------------------------------------
# idea.log.path=${idea.system.path}/log

#---------------------------------------------------------------------
# Maximum file size (in KiB) IDE should provide code assistance for.
# The larger file is the slower its editor works and higher overall system memory requirements are
# if code assistance is enabled. Remove this property or set to very large number if you need
# code assistance for any files available regardless of their size.
#---------------------------------------------------------------------
idea.max.intellisense.filesize=2500

#---------------------------------------------------------------------
# Maximum file size (in KiB) the IDE is able to open.
#---------------------------------------------------------------------
idea.max.content.load.filesize=20000

#---------------------------------------------------------------------
# This option controls console cyclic buffer: keeps the console output size not higher than the specified buffer size (KiB).
# Older lines are deleted. In order to disable cycle buffer use idea.cycle.buffer.size=disabled
#---------------------------------------------------------------------
idea.cycle.buffer.size=1024

#---------------------------------------------------------------------
# Configure if a special launcher should be used when running processes from within IDE.
# Using Launcher enables "soft exit" and "thread dump" features
#---------------------------------------------------------------------
idea.no.launcher=false

#---------------------------------------------------------------------
# To avoid too long classpath
#---------------------------------------------------------------------
idea.dynamic.classpath=false

#---------------------------------------------------------------------
# There are two possible values of idea.popup.weight property: "heavy" and "medium".
# If you have WM configured as "Focus follows mouse with Auto Raise" then you have to
# set this property to "medium". It prevents problems with popup menus on some
# configurations.
#---------------------------------------------------------------------
idea.popup.weight=heavy

#---------------------------------------------------------------------
# Removing this property may lead to editor performance degradation under Windows.
#---------------------------------------------------------------------
sun.java2d.d3d=false

#---------------------------------------------------------------------
# Removing this property may lead to editor performance degradation on Java 8+.
#---------------------------------------------------------------------
swing.bufferPerWindow=true

#---------------------------------------------------------------------
# Removing this property may lead to editor performance degradation under X Window.
#---------------------------------------------------------------------
sun.java2d.pmoffscreen=false

#---------------------------------------------------------------------
# Enables HiDPI support in JBR
#---------------------------------------------------------------------
sun.java2d.uiScale.enabled=true

#---------------------------------------------------------------------
# Applicable to the Swing text components displaying HTML (except JEditorPane).
# Rebases CSS size map depending on the component's font size to let relative
# font size values (smaller, larger) scale properly. JBR-only.
#---------------------------------------------------------------------
javax.swing.rebaseCssSizeMap=true


#---------------------------------------------------------------------
# Workaround for accessing (in terms of a11y) long VCS logs on macOS. JBR-only.
#---------------------------------------------------------------------
sun.awt.mac.a11y.tableAccessibleRowCountThreshold=1000

#---------------------------------------------------------------------
# Enabling an optimization that excludes traversal of collapsed accessible nodes from the accessible tree. JBR-4167
#---------------------------------------------------------------------
javax.swing.JTree.excludeAccessibleChildrenFromClosedNodes=true

#---------------------------------------------------------------------
# Workaround to avoid long hangs while accessing clipboard under Mac OS X.
#---------------------------------------------------------------------
#ide.mac.useNativeClipboard=True

#---------------------------------------------------------------------
# Maximum size (KiB) the IDE will use to show historical file contents -
# in Show Diff or when calculating Digest Diff
#---------------------------------------------------------------------
#idea.max.vcs.loaded.size.kb=20480

#---------------------------------------------------------------------
# IDEA file chooser peeks inside directories to detect whether they contain a valid project
# (to mark such directories with a corresponding icon).
# Uncommenting the option prevents this behavior outside the user home directory.
#---------------------------------------------------------------------
#idea.chooser.lookup.for.project.dirs=false

#---------------------------------------------------------------------
# In LWCToolkit.invokeAndWait() listens to EDT state and disposes the invocation event
# when EDT becomes free but the invocation event is not yet dispatched (considered lost).
# This prevents a deadlock and makes the invocation return some default result.
#---------------------------------------------------------------------
sun.lwawt.macosx.LWCToolkit.invokeAndWait.disposeOnEDTFree=true

#---------------------------------------------------------------------
# Experimental options that do a number of things to make truly smooth scrolling possible:
#
# * Enables hardware-accelerated scrolling.
#     Blit-acceleration copies as much of the rendered area as possible and then repaints only newly exposed region.
#     This helps to improve scrolling performance and to reduce CPU usage (especially if drawing is compute-intensive).
#
# * Enables "true double buffering".
#     True double buffering is needed to eliminate tearing on blit-accelerated scrolling and to restore
#     frame buffer content without the usual repainting, even when the EDT is blocked.
#
# * Adds "idea.true.smooth.scrolling.debug" option.
#     Checks whether blit-accelerated scrolling is feasible, and if so, checks whether true double buffering is available.
#
# * Enables handling of high-precision mouse wheel events.
#     Although Java 7 introduced MouseWheelEven.getPreciseWheelRotation() method, JScrollPane doesn't use it so far.
#     Depends on the Editor / General / Smooth Scrolling setting, remote desktop detection and power save mode state.
#     Ideally, we need to patch the runtime (on Windows, Linux and macOS) to improve handling of the fine-grained input data.
#     This feature can be toggled via "idea.true.smooth.scrolling.high.precision" option.
#
# * Enables handling of pixel-perfect scrolling events.
#     Currently, this mode is available only under macOS with JetBrains Runtime.
#     This feature can be toggled via "idea.true.smooth.scrolling.pixel.perfect" option.
#
# * Enables interpolation of scrolling input (scrollbar, mouse wheel, touchpad, keys, etc).
#     Smooths input, which lacks both spatial and temporal resolution, performs the rendering asynchronously.
#     Depends on the Editor / General / Smooth Scrolling setting, remote desktop detection and power save mode state.
#     The feature can be tweaked using the following options:
#       "idea.true.smooth.scrolling.interpolation" - the main switch
#       "idea.true.smooth.scrolling.interpolation.scrollbar" - scrollbar interpolation
#       "idea.true.smooth.scrolling.interpolation.scrollbar.delay" - initial delay for scrollbar interpolation (ms)
#       "idea.true.smooth.scrolling.interpolation.mouse.wheel" - mouse wheel / touchpad interpolation
#       "idea.true.smooth.scrolling.interpolation.mouse.wheel.delay.min" - minimum initial delay for mouse wheel interpolation (ms)
#       "idea.true.smooth.scrolling.interpolation.mouse.wheel.delay.max" - maximum initial delay for mouse wheel interpolation (ms)
#       "idea.true.smooth.scrolling.interpolation.precision.touchpad" - touchpad interpolation
#       "idea.true.smooth.scrolling.interpolation.precision.touchpad.delay" - initial delay for touchpad interpolation (ms)
#       "idea.true.smooth.scrolling.interpolation.other" - interpolation of other input sources
#       "idea.true.smooth.scrolling.interpolation.other.delay" - initial delay for other input source interpolation (ms)
#
# * Adds on-demand horizontal scrollbar in editor.
#     The horizontal scrollbar is shown only when it's actually needed for currently visible content.
#     This helps to save editor space and to prevent occasional horizontal "jitter" on vertical touchpad scrolling.
#     This feature can be toggled via "idea.true.smooth.scrolling.dynamic.scrollbars" option.
#---------------------------------------------------------------------
#idea.true.smooth.scrolling=true

#-----------------------------------------------------------------------
# Change to 'enabled' if you want to receive instant visual notifications
# about fatal errors that happen to an IDE or plugins installed.
#-----------------------------------------------------------------------
idea.fatal.error.notification=disabled
