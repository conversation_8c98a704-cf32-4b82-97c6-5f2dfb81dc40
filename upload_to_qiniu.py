#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的七牛云上传脚本
用于快速上传Android Studio到CDN
"""

import os
import sys
import json
import argparse
from qiniu_uploader import QiniuUploader

def load_config(config_file='config.json'):
    """加载配置文件"""
    if not os.path.exists(config_file):
        print(f"❌ 配置文件不存在: {config_file}")
        print("请先创建配置文件，参考 config.json 模板")
        sys.exit(1)
    
    with open(config_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def detect_platform(file_path):
    """根据文件扩展名检测平台"""
    ext = os.path.splitext(file_path)[1].lower()
    
    if ext in ['.exe', '.msi']:
        return 'windows'
    elif ext in ['.dmg', '.pkg']:
        return 'mac'
    elif ext in ['.tar.gz', '.deb', '.rpm', '.appimage']:
        return 'linux'
    else:
        return 'unknown'

def get_file_size_mb(file_path):
    """获取文件大小（MB）"""
    size_bytes = os.path.getsize(file_path)
    return size_bytes / (1024 * 1024)

def main():
    parser = argparse.ArgumentParser(description='上传文件到七牛云CDN')
    parser.add_argument('file_path', help='要上传的文件路径')
    parser.add_argument('--version', '-v', required=True, help='版本号，如 2025.1.1')
    parser.add_argument('--platform', '-p', help='平台 (windows/mac/linux)，不指定则自动检测')
    parser.add_argument('--config', '-c', default='config.json', help='配置文件路径')
    parser.add_argument('--custom-key', '-k', help='自定义远程文件key')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.file_path):
        print(f"❌ 文件不存在: {args.file_path}")
        sys.exit(1)
    
    # 加载配置
    config = load_config(args.config)
    qiniu_config = config['qiniu']
    
    # 检查配置
    if qiniu_config['access_key'] == 'YOUR_QINIU_ACCESS_KEY':
        print("❌ 请先在配置文件中设置正确的七牛云AccessKey和SecretKey")
        sys.exit(1)
    
    # 检测平台
    platform = args.platform or detect_platform(args.file_path)
    if platform == 'unknown':
        print(f"❌ 无法检测文件平台，请使用 --platform 参数指定")
        sys.exit(1)
    
    # 显示文件信息
    file_size_mb = get_file_size_mb(args.file_path)
    print(f"📁 文件: {args.file_path}")
    print(f"📏 大小: {file_size_mb:.1f} MB")
    print(f"🖥️  平台: {platform}")
    print(f"🏷️  版本: {args.version}")
    print()
    
    # 确认上传
    confirm = input("确认上传到七牛云CDN？(y/N): ")
    if confirm.lower() not in ['y', 'yes']:
        print("❌ 取消上传")
        sys.exit(0)
    
    # 初始化上传器
    uploader = QiniuUploader(
        qiniu_config['access_key'],
        qiniu_config['secret_key'],
        qiniu_config['bucket_name'],
        qiniu_config['domain']
    )
    
    try:
        if args.custom_key:
            # 使用自定义key上传
            result = uploader.upload_file(args.file_path, args.custom_key)
        else:
            # 上传Android Studio
            result = uploader.upload_android_studio(args.file_path, args.version, platform)
        
        if result['success']:
            print(f"\n🎉 上传成功！")
            print(f"📎 下载链接: {result['url']}")
            print(f"🔑 文件Key: {result['key']}")
            print(f"📊 文件大小: {result['size']} bytes")
            
            # 刷新CDN缓存
            print("\n🔄 刷新CDN缓存...")
            if uploader.refresh_cache([result['url']]):
                print("✅ CDN缓存刷新成功")
            else:
                print("⚠️ CDN缓存刷新失败，请手动刷新")
        else:
            print(f"\n❌ 上传失败: {result.get('error', '未知错误')}")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 上传过程中发生错误: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
