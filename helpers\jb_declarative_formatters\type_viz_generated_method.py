from __future__ import annotations

import abc
from typing import Optional, List


class GeneratedMethod:
    """
    The class represents synthetically generated method identifiers for item getters.
    These method identifiers are generated by [TypeVizTopLevelMethods] based on the Natvis item name and provide
    a string expression with a method call, which can be used in later expression evaluations.
    """
    class MethodIdentifier(abc.ABC):
        @abc.abstractmethod
        def make_call_expr(self, this_reference: str, args: List[str]) -> str:
            return ""

        @abc.abstractmethod
        def get_name(self) -> str:
            return ""

    class Named(MethodIdentifier):
        def __init__(self, name: str):
            self._name = name

        def make_call_expr(self, this_reference: str, args: List[str]) -> str:
            return f"{this_reference}.{self._name}({', '.join(args)})"

        def get_name(self) -> str:
            return self._name

    class SubscriptOperator(MethodIdentifier):
        def make_call_expr(self, this_reference: str, args: List[str]) -> str:
            return f"{this_reference}[{', '.join(args)}]"

        def get_name(self) -> str:
            return "operator[]"

    class Call:
        def __init__(self, identifier: GeneratedMethod.MethodIdentifier, args: List[str]):
            self._identifier = identifier
            self._args = args

        def make_call_expr(self, this_reference: str) -> str:
            return self._identifier.make_call_expr(this_reference, self._args)

    @staticmethod
    def named_method(name: str) -> GeneratedMethod:
        return GeneratedMethod(GeneratedMethod.Named(name))

    @staticmethod
    def subscript_operator() -> GeneratedMethod:
        return GeneratedMethod(GeneratedMethod.SubscriptOperator())

    def __init__(self, identifier: GeneratedMethod.MethodIdentifier):
        self._identifier = identifier

    @property
    def identifier(self) -> MethodIdentifier:
        return self._identifier

    @property
    def name(self) -> str:
        return self._identifier.get_name()

    def method_call(self, args: Optional[List[str]] = None) -> Call:
        return GeneratedMethod.Call(self._identifier, args or [])


class GeneratedMethodDefinition:
    """
    The class represents synthetically generated method definitions for item getters.
    These method definitions are generated from Natvis item expressions and mostly represent getters that return the expressions themselves.
    The definitions are generated by [TypeVizTopLevelMethods] after generating the identifier [GeneratedMethod] and are used later for
    declaring top-level lazy declarations via [SBDebugger.AddTopLevelLazyDeclaration] and [SBDebugger.AddTopLevelLazyDeclarationByRegex].
    """
    def __init__(self, full_name: str, body_substitution: str, name_uses_regex: bool):
        self.full_name = full_name
        self.body_substitution = body_substitution
        self.name_uses_regex = name_uses_regex
