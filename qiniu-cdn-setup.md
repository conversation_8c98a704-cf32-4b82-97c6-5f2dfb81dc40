# 七牛云CDN分发配置指南

## 1. 七牛云账号准备

### 注册和认证
1. 访问 [七牛云官网](https://www.qiniu.com/) 注册账号
2. 完成实名认证
3. 获取 AccessKey 和 SecretKey

### 创建存储空间
1. 登录七牛云控制台
2. 进入"对象存储" → "空间管理"
3. 创建新的存储空间（Bucket）
   - 空间名称：例如 `android-studio-dist`
   - 存储区域：选择离用户最近的区域
   - 访问控制：公开空间（用于CDN分发）

## 2. CDN域名配置

### 绑定自定义域名
1. 进入"CDN" → "域名管理"
2. 添加加速域名
   - 加速域名：例如 `cdn.yourdomain.com`
   - 通信协议：HTTPS（推荐）
   - 源站配置：选择七牛云存储
   - 源站域名：选择刚创建的存储空间

### SSL证书配置
1. 申请或上传SSL证书
2. 配置HTTPS访问
3. 设置HTTP自动跳转HTTPS

## 3. 文件上传策略

### 大文件分片上传
```json
{
  "scope": "android-studio-dist",
  "deadline": 1893456000,
  "insertOnly": 1,
  "fsizeLimit": 10737418240,
  "mimeLimit": "application/octet-stream;application/zip;application/x-msdownload"
}
```

### 目录结构建议
```
/android-studio/
  /windows/
    /2025.1.1/
      android-studio-2025.1.1-windows.exe
      android-studio-2025.1.1-windows.zip
  /mac/
    /2025.1.1/
      android-studio-2025.1.1-mac.dmg
  /linux/
    /2025.1.1/
      android-studio-2025.1.1-linux.tar.gz
  /plugins/
    /chinese-language-pack/
      chinese-language-pack-latest.zip
```

## 4. 缓存策略配置

### 缓存规则设置
1. 进入CDN域名管理 → 缓存配置
2. 设置不同文件类型的缓存时间：
   - 安装包文件(.exe, .dmg, .tar.gz)：缓存30天
   - 配置文件(.properties, .vmoptions)：缓存1天
   - 版本信息文件(.json)：缓存1小时

### 缓存刷新
- 支持URL刷新和目录刷新
- 新版本发布时及时刷新缓存

## 5. 访问控制

### 防盗链设置
1. 配置Referer白名单
2. 设置User-Agent限制
3. IP访问限制（如需要）

### 时间戳防盗链
```
http://cdn.yourdomain.com/android-studio/windows/2025.1.1/android-studio-2025.1.1-windows.exe?e=1640995200&token=abcdef123456
```

## 6. 监控和统计

### 流量监控
- 实时监控CDN流量使用情况
- 设置流量告警阈值
- 查看访问日志和统计报表

### 性能监控
- 监控响应时间
- 查看命中率统计
- 分析用户访问分布

## 下一步操作

1. 创建上传脚本
2. 配置自动化部署
3. 设置版本管理系统
4. 创建下载页面
